import openrouteservice
import time
import pandas as pd
from tqdm import tqdm
from sklearn.cluster import KMeans
import numpy as np
import math
import joblib
import os
import logging
from typing import List, Dict, Tuple, Optional
from fastapi import FastAPI, HTTPException
from fastapi.responses import HTMLResponse
from pydantic import BaseModel, Field
import uvicorn

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI app initialization
app = FastAPI(
    title="Zoning API",
    description="API for creating service zones using dynamic clustering",
    version="1.0.0"
)

# Configuration
API_KEY = "5b3ce3597851110001cf62482188830654e446da8fe7ab335c8bd2a1"
MAX_CUSTOMERS_PER_ZONE = 11  # Optimal customers per zone

# Initialize the ORS client
client = openrouteservice.Client(key=API_KEY)

# Rate limit delays (seconds per request to stay under per-minute limits)
RATE_LIMITS = {
    "optimization": 60 / 40,  # 40 per minute
    "directions": 60 / 40,    # 40 per minute
    "geocoding": 60 / 40,     # 40 per minute
}

# Cache setup
cache_dir = "ors_cache"
os.makedirs(cache_dir, exist_ok=True)
memory = joblib.Memory(cache_dir, verbose=0)

# Pydantic models for API responses
class ZoneCustomer(BaseModel):
    id: int = Field(..., description="Customer ID")
    latitude: float = Field(..., description="Customer latitude")
    longitude: float = Field(..., description="Customer longitude")
    address: str = Field(..., description="Customer address")

class DynamicZone(BaseModel):
    zone_id: int = Field(..., description="Zone identifier")
    customers: List[ZoneCustomer] = Field(..., description="Customers assigned to this zone")
    customer_count: int = Field(..., description="Number of customers in zone")
    center_latitude: float = Field(..., description="Zone center latitude")
    center_longitude: float = Field(..., description="Zone center longitude")

class DynamicZoningResponse(BaseModel):
    total_customers: int = Field(..., description="Total number of customers processed")
    routable_customers: int = Field(..., description="Number of customers with routable coordinates")
    total_zones: int = Field(..., description="Total number of zones created")
    max_customers_per_zone: int = Field(..., description="Maximum customers allowed per zone")
    zones: List[DynamicZone] = Field(..., description="List of all zones with their customers")
    computation_time_ms: float = Field(..., description="Time taken to compute zones in milliseconds")

# Core routing functions
def load_and_process_customers(filepath: str) -> List[Dict]:
    """Load customer data and create customers list"""
    logger.info(f"Loading data from {filepath}...")
    df = pd.read_csv(filepath)
    logger.info(f"Loaded {len(df)} customer records")

    # Create customers list with correct column names
    customers = [
        {
            "id": idx,
            "address": f"Customer {idx}",
            "coords": [
                float(row['LON']),
                float(row['LAT'])
            ]
        }
        for idx, row in df.iterrows()
    ]
    return customers

# Step 1: Check if coordinates are routable within 350m radius
@memory.cache
def cached_directions(coordinates, profile, format, radiuses):
    time.sleep(RATE_LIMITS["directions"])
    return client.directions(
        coordinates=coordinates,
        profile=profile,
        format=format,
        radiuses=radiuses
    )

def is_routable(coords):
    try:
        cached_directions(
            coordinates=[coords, coords],
            profile="driving-car",
            format="geojson",
            radiuses=[350]
        )
        logger.debug(f"Coordinates {coords} are routable")
        return True
    except Exception as e:
        logger.debug(f"Coordinates {coords} not routable: {e}")
        return False

# NEW GEOCODING AND SNAPPING FUNCTIONS FOR GOOGLE MAPS-LIKE BEHAVIOR

@memory.cache
def cached_geocoding(address):
    """Cached geocoding function with rate limiting"""
    time.sleep(RATE_LIMITS["geocoding"])
    return client.pelias_search(text=address)

def geocode_address(address: str) -> Optional[List[float]]:
    """
    Geocode an address using ORS Pelias geocoding API.
    Returns [longitude, latitude] coordinates or None if geocoding fails.
    """
    try:
        logger.info(f"Geocoding address: {address}")
        result = cached_geocoding(address)

        if result and 'features' in result and len(result['features']) > 0:
            feature = result['features'][0]
            coords = feature['geometry']['coordinates']
            logger.info(f"Geocoded '{address}' to coordinates: {coords}")
            return coords  # [longitude, latitude]
        else:
            logger.warning(f"No geocoding results found for address: {address}")
            return None

    except Exception as e:
        logger.error(f"Geocoding failed for address '{address}': {e}")
        return None

def snap_to_nearest_road(coords: List[float], max_radius: int = 5000) -> Optional[List[float]]:
    """
    Snap coordinates to the nearest routable road using ORS directions API with radiuses.

    Args:
        coords: [longitude, latitude] coordinates
        max_radius: Maximum search radius in meters (default 5000m = 5km)

    Returns:
        [longitude, latitude] of nearest routable point or None if no road found
    """
    try:
        logger.info(f"Snapping coordinates {coords} to nearest road within {max_radius}m")

        # Try different radiuses starting from 350m up to max_radius
        radiuses = [350, 1000, 2000, max_radius]

        for radius in radiuses:
            try:
                # Use the directions API with a small route to find nearest routable point
                # Create a very short route from the point to itself with increasing radius
                result = cached_directions(
                    coordinates=[coords, coords],
                    profile="driving-car",
                    format="geojson",
                    radiuses=[radius, radius]
                )

                if result and 'features' in result and len(result['features']) > 0:
                    # Extract the snapped coordinates from the route
                    route_coords = result['features'][0]['geometry']['coordinates']
                    if route_coords and len(route_coords) > 0:
                        snapped_coords = route_coords[0]  # First coordinate is the snapped start point
                        logger.info(f"Successfully snapped to nearest road: {snapped_coords} (radius: {radius}m)")
                        return snapped_coords

            except Exception as e:
                logger.debug(f"Snapping failed at radius {radius}m: {e}")
                continue

        logger.warning(f"Could not snap coordinates {coords} to any road within {max_radius}m")
        return None

    except Exception as e:
        logger.error(f"Road snapping failed for coordinates {coords}: {e}")
        return None

def process_address_with_fallback(address: str, existing_coords: Optional[List[float]] = None) -> Optional[List[float]]:
    """
    Process an address with Google Maps-like behavior:
    1. If coordinates are provided and routable, use them
    2. If coordinates are provided but not routable, try to snap to nearest road
    3. If no coordinates or snapping fails, try geocoding the address
    4. If geocoding succeeds, try to snap those coordinates to nearest road

    Args:
        address: Street address to process
        existing_coords: Optional [longitude, latitude] coordinates

    Returns:
        [longitude, latitude] of routable coordinates or None if all methods fail
    """
    logger.info(f"Processing address with fallback: '{address}' with coords: {existing_coords}")

    # Step 1: If we have coordinates, check if they're routable
    if existing_coords:
        if is_routable(existing_coords):
            logger.info(f"Existing coordinates {existing_coords} are routable")
            return existing_coords

        # Step 2: Try to snap existing coordinates to nearest road
        logger.info(f"Existing coordinates {existing_coords} not routable, attempting to snap to nearest road")
        snapped_coords = snap_to_nearest_road(existing_coords)
        if snapped_coords and is_routable(snapped_coords):
            logger.info(f"Successfully snapped coordinates to routable point: {snapped_coords}")
            return snapped_coords

    # Step 3: Try geocoding the address
    logger.info(f"Attempting to geocode address: '{address}'")
    geocoded_coords = geocode_address(address)
    if geocoded_coords:
        # Step 4: Check if geocoded coordinates are routable
        if is_routable(geocoded_coords):
            logger.info(f"Geocoded coordinates {geocoded_coords} are routable")
            return geocoded_coords

        # Step 5: Try to snap geocoded coordinates to nearest road
        logger.info(f"Geocoded coordinates {geocoded_coords} not routable, attempting to snap")
        snapped_coords = snap_to_nearest_road(geocoded_coords)
        if snapped_coords and is_routable(snapped_coords):
            logger.info(f"Successfully snapped geocoded coordinates to routable point: {snapped_coords}")
            return snapped_coords

    logger.error(f"All fallback methods failed for address: '{address}'")
    return None

def filter_routable_customers(customers: List[Dict]) -> Tuple[List[Dict], List[Dict]]:
    """
    Filter and process customer coordinates with Google Maps-like behavior.
    Attempts to geocode addresses and snap to nearest roads for non-routable coordinates.
    """
    logger.info("Processing customer coordinates with Google Maps-like fallback behavior...")
    routable_customers = []
    non_routable_coords = []

    for customer in tqdm(customers, desc="Processing customer coords"):
        original_coords = customer["coords"]
        address = customer["address"]

        # Try to get routable coordinates using the fallback process
        routable_coords = process_address_with_fallback(address, original_coords)

        if routable_coords:
            # Create updated customer with routable coordinates
            updated_customer = customer.copy()
            updated_customer["coords"] = routable_coords
            updated_customer["original_coords"] = original_coords  # Keep track of original
            updated_customer["coords_source"] = "processed" if routable_coords != original_coords else "original"
            routable_customers.append(updated_customer)

            if routable_coords != original_coords:
                logger.info(f"Customer {customer['id']}: Updated coordinates from {original_coords} to {routable_coords}")
        else:
            logger.warning(f"Could not find routable coordinates for customer {customer['id']} with address '{address}'")
            non_routable_coords.append({
                "Customer_ID": customer["id"],
                "Address": customer["address"],
                "Original_LON": original_coords[0],
                "Original_LAT": original_coords[1],
                "Reason": "All fallback methods failed"
            })

    # Save non-routable coordinates to CSV
    if non_routable_coords:
        non_routable_df = pd.DataFrame(non_routable_coords)
        non_routable_df.to_csv("non_routable_coordinates.csv", index=False)
        logger.info(f"Saved {len(non_routable_coords)} non-routable coordinates to non_routable_coordinates.csv")
    else:
        logger.info("All customer coordinates were successfully processed to routable points")

    logger.info(f"Successfully processed {len(routable_customers)} routable customers out of {len(customers)} total")
    return routable_customers, non_routable_coords

# NEW DYNAMIC ZONING FUNCTIONS
def calculate_dynamic_zones(num_customers: int, max_per_zone: int = MAX_CUSTOMERS_PER_ZONE) -> int:
    """Calculate optimal number of zones for dynamic clustering"""
    import math
    return math.ceil(num_customers / max_per_zone)

def dynamic_cluster_customers(customers: List[Dict], max_per_zone: int = MAX_CUSTOMERS_PER_ZONE) -> List[List[Dict]]:
    """
    Improved clustering that ensures customers are assigned to their closest technician.
    Uses iterative optimization to minimize customer-to-technician distances.
    """
    import time
    start_time = time.time()

    if not customers:
        return []

    # Calculate number of zones needed
    num_zones = calculate_dynamic_zones(len(customers), max_per_zone)

    # Extract coordinates for clustering
    coords = np.array([customer["coords"] for customer in customers])

    # Step 1: Initial K-means clustering to get starting zone centers
    kmeans = KMeans(n_clusters=num_zones, n_init=10, random_state=42)
    kmeans.fit(coords)
    zone_centers = kmeans.cluster_centers_.copy()

    logger.info(f"Initial K-means clustering completed, now optimizing for closest assignments...")

    # Step 2: Iterative optimization to ensure customers are assigned to closest technician
    max_iterations = 10
    for iteration in range(max_iterations):
        # Create zones based on current centers
        zones = [[] for _ in range(num_zones)]

        # Assign each customer to closest zone center that has capacity
        for customer in customers:
            customer_coord = np.array(customer["coords"])

            # Calculate distances to all zone centers
            zone_distances = []
            for zone_idx, center in enumerate(zone_centers):
                dist = np.sqrt(np.sum((customer_coord - center) ** 2))
                zone_distances.append((dist, zone_idx))

            # Sort by distance (closest first)
            zone_distances.sort(key=lambda x: x[0])

            # Assign to closest zone that has capacity
            assigned = False
            for dist, zone_idx in zone_distances:
                if len(zones[zone_idx]) < max_per_zone:
                    customer_copy = customer.copy()
                    customer_copy["zone_id"] = zone_idx
                    zones[zone_idx].append(customer_copy)
                    assigned = True
                    break

            # If no zone has capacity (shouldn't happen), assign to closest anyway
            if not assigned:
                closest_zone = zone_distances[0][1]
                customer_copy = customer.copy()
                customer_copy["zone_id"] = closest_zone
                zones[closest_zone].append(customer_copy)

        # Step 3: Balance zones by distance
        zones = balance_zones_by_distance(zones, max_per_zone, zone_centers)

        # Step 4: Recalculate zone centers based on new assignments
        new_centers = []
        for zone in zones:
            if zone:  # Non-empty zone
                zone_coords = np.array([c["coords"] for c in zone])
                center = np.mean(zone_coords, axis=0)
                new_centers.append(center)
            else:
                # Keep original center for empty zones
                new_centers.append(zone_centers[len(new_centers)])

        # Check for convergence
        center_movement = np.mean([np.linalg.norm(new - old) for new, old in zip(new_centers, zone_centers)])
        zone_centers = np.array(new_centers)

        if center_movement < 0.001:  # Convergence threshold
            logger.info(f"Converged after {iteration + 1} iterations")
            break

    # Final verification: ensure no zone exceeds max_per_zone
    max_zone_size = max(len(zone) for zone in zones if zone)
    min_zone_size = min(len(zone) for zone in zones if zone)

    logger.info(f"Final zone sizes - Max: {max_zone_size}, Min: {min_zone_size}, Target: ≤{max_per_zone}")

    if max_zone_size > max_per_zone:
        logger.warning(f"Some zones exceed {max_per_zone} customers. Running final balancing...")
        zones = balance_dynamic_zones(zones, max_per_zone)  # Use legacy balancing as final safety net

    # Post-optimization: Fix geographical mismatches
    zones = fix_geographical_mismatches(zones, zone_centers, max_per_zone)

    end_time = time.time()
    computation_time = (end_time - start_time) * 1000

    logger.info(f"Improved clustering completed in {computation_time:.2f}ms for {len(customers)} customers into {len(zones)} zones")

    return zones, zone_centers, computation_time

def balance_zones_by_distance(zones: List[List[Dict]], max_per_zone: int, zone_centers: np.ndarray) -> List[List[Dict]]:
    """
    Improved zone balancing that considers distance when moving customers.
    """
    # Remove empty zones
    zones = [zone for zone in zones if zone]

    # Redistribute customers from oversized zones
    max_iterations = 50
    iteration = 0

    while iteration < max_iterations:
        moved_customer = False
        iteration += 1

        for zone_idx, zone in enumerate(zones):
            if len(zone) > max_per_zone:
                # Find the customer in this zone who is closest to another zone
                best_customer_to_move = None
                best_target_zone = None
                best_distance_improvement = 0

                for customer in zone:
                    customer_coord = np.array(customer["coords"])
                    current_distance = np.linalg.norm(customer_coord - zone_centers[zone_idx])

                    # Check all other zones that have space
                    for target_zone_idx, target_zone in enumerate(zones):
                        if target_zone_idx != zone_idx and len(target_zone) < max_per_zone:
                            target_distance = np.linalg.norm(customer_coord - zone_centers[target_zone_idx])
                            distance_improvement = current_distance - target_distance

                            # If this customer would be closer to the target zone
                            if distance_improvement > best_distance_improvement:
                                best_distance_improvement = distance_improvement
                                best_customer_to_move = customer
                                best_target_zone = target_zone_idx

                # Move the best customer if found
                if best_customer_to_move and best_target_zone is not None:
                    zone.remove(best_customer_to_move)
                    best_customer_to_move["zone_id"] = best_target_zone
                    zones[best_target_zone].append(best_customer_to_move)
                    moved_customer = True
                    logger.info(f"Moved customer {best_customer_to_move['id']} from zone {zone_idx} to zone {best_target_zone}")
                    break

        if not moved_customer:
            break

    return zones

def balance_dynamic_zones(zones: List[List[Dict]], max_per_zone: int) -> List[List[Dict]]:
    """Legacy balance function - kept for compatibility"""
    # Remove empty zones
    zones = [zone for zone in zones if zone]

    # Redistribute customers from oversized zones
    balanced = True
    while balanced:
        balanced = False

        for zone in zones:
            if len(zone) > max_per_zone:
                # Find zone with minimum customers
                min_zone_idx = min(range(len(zones)), key=lambda x: len(zones[x]))

                # Move customer to zone with fewer customers
                if len(zones[min_zone_idx]) < max_per_zone:
                    customer_to_move = zone.pop()
                    customer_to_move["zone_id"] = min_zone_idx
                    zones[min_zone_idx].append(customer_to_move)
                    balanced = True
                    break

    return zones

def fix_geographical_mismatches(zones: List[List[Dict]], zone_centers: np.ndarray, max_per_zone: int) -> List[List[Dict]]:
    """
    Post-optimization step to fix customers assigned to distant technicians
    when a closer technician has available capacity.
    """
    logger.info("Checking for geographical mismatches...")

    improvements_made = 0
    max_swaps = 50  # Prevent infinite loops

    for _ in range(max_swaps):
        swap_made = False

        # Check each customer in each zone
        for current_zone_idx, current_zone in enumerate(zones):
            if not current_zone:
                continue

            customers_to_check = current_zone.copy()  # Copy to avoid modification during iteration

            for customer in customers_to_check:
                customer_coord = np.array(customer["coords"])
                current_distance = np.linalg.norm(customer_coord - zone_centers[current_zone_idx])

                # Find if there's a closer technician with available capacity
                best_alternative = None
                best_distance = current_distance
                best_zone_idx = None

                for other_zone_idx, other_zone in enumerate(zones):
                    if other_zone_idx == current_zone_idx:
                        continue

                    # Check if this zone has capacity
                    if len(other_zone) < max_per_zone:
                        other_distance = np.linalg.norm(customer_coord - zone_centers[other_zone_idx])

                        # If this technician is significantly closer (at least 10% improvement)
                        if other_distance < best_distance * 0.9:
                            best_distance = other_distance
                            best_alternative = other_zone
                            best_zone_idx = other_zone_idx

                # Make the swap if we found a better assignment
                if best_alternative is not None:
                    # Remove from current zone
                    current_zone.remove(customer)

                    # Add to better zone
                    customer["zone_id"] = best_zone_idx
                    best_alternative.append(customer)

                    improvements_made += 1
                    swap_made = True

                    distance_improvement = current_distance - best_distance
                    logger.info(f"Moved customer {customer['id']} from zone {current_zone_idx} to zone {best_zone_idx} (distance improvement: {distance_improvement:.4f})")

                    break  # Only one swap per zone per iteration to avoid conflicts

            if swap_made:
                break  # Start over to recalculate after each swap

        if not swap_made:
            break  # No more improvements possible

    logger.info(f"Geographical mismatch fixing completed. Made {improvements_made} improvements.")
    return zones

def perform_dynamic_zoning(filepath: str = "san_antonio_coordinates.csv") -> Dict:
    """
    Main function for dynamic zoning - computes zones on-the-fly without storing in database.
    This function is called every time a new customer is added or map refresh is needed.
    """
    import time
    start_time = time.time()

    try:
        # Load customer data (simulating database fetch)
        customers = load_and_process_customers(filepath)

        if not customers:
            return {
                "total_customers": 0,
                "routable_customers": 0,
                "total_zones": 0,
                "max_customers_per_zone": MAX_CUSTOMERS_PER_ZONE,
                "zones": [],
                "computation_time_ms": 0.0
            }

        # Apply Google Maps-like address processing to dynamic zones too!
        routable_customers, _ = filter_routable_customers(customers)

        if not routable_customers:
            logger.warning("No routable customers found after address processing")
            return {
                "total_customers": len(customers),
                "routable_customers": 0,
                "total_zones": 0,
                "max_customers_per_zone": MAX_CUSTOMERS_PER_ZONE,
                "zones": [],
                "computation_time_ms": 0.0
            }

        # Perform dynamic clustering on routable customers
        zones, cluster_centers, _ = dynamic_cluster_customers(routable_customers, MAX_CUSTOMERS_PER_ZONE)

        # Format response
        formatted_zones = []
        for zone_id, zone_customers in enumerate(zones):
            if zone_customers:  # Only include non-empty zones
                # Calculate zone center
                if zone_id < len(cluster_centers):
                    center_lon, center_lat = cluster_centers[zone_id]
                else:
                    # Fallback: calculate center from customers
                    lats = [c["coords"][1] for c in zone_customers]
                    lons = [c["coords"][0] for c in zone_customers]
                    center_lat = sum(lats) / len(lats)
                    center_lon = sum(lons) / len(lons)

                # Format customers for this zone
                zone_customer_list = []
                for customer in zone_customers:
                    zone_customer_list.append({
                        "id": customer["id"],
                        "latitude": customer["coords"][1],
                        "longitude": customer["coords"][0],
                        "address": customer["address"]
                    })

                formatted_zones.append({
                    "zone_id": zone_id + 1,  # 1-based indexing
                    "customers": zone_customer_list,
                    "customer_count": len(zone_customers),
                    "center_latitude": center_lat,
                    "center_longitude": center_lon
                })

        end_time = time.time()
        total_time = (end_time - start_time) * 1000  # Convert to milliseconds

        logger.info(f"Dynamic zoning completed: {len(customers)} total customers, {len(routable_customers)} routable -> {len(formatted_zones)} zones in {total_time:.2f}ms")

        # Prepare result data
        result_data = {
            "total_customers": len(customers),
            "routable_customers": len(routable_customers),
            "total_zones": len(formatted_zones),
            "max_customers_per_zone": MAX_CUSTOMERS_PER_ZONE,
            "zones": formatted_zones,
            "computation_time_ms": round(total_time, 2)
        }

        # Store zones in JSON file
        try:
            import json
            with open("dynamic_zones.json", "w") as f:
                json.dump(result_data, f, indent=2, default=str)
            logger.info(f"Zones saved to dynamic_zones.json")
        except Exception as e:
            logger.warning(f"Failed to save zones to JSON: {e}")

        return result_data

    except Exception as e:
        logger.error(f"Error in dynamic zoning: {e}")
        raise

# FastAPI Endpoints
@app.get("/", response_class=HTMLResponse)
async def root():
    """Root endpoint with API documentation"""
    return """
    <html>
        <head>
            <title>Zoning API</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
                .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                h1 { color: #2c3e50; margin-bottom: 10px; }
                h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px; margin-top: 30px; }
                .subtitle { color: #7f8c8d; margin-bottom: 30px; font-size: 16px; }
                ul { margin: 15px 0; }
                li { margin: 8px 0; }
                .endpoint { background: #ecf0f1; padding: 3px 6px; border-radius: 3px; font-family: monospace; }
                .status { display: inline-block; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-left: 10px; }
                .active { background: #2ecc71; color: white; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🗺️ Zoning API</h1>
                <p class="subtitle">API for creating service zones using dynamic clustering</p>

                <h2>🎯 Available Endpoints</h2>
                <ul>
                    <li><span class="endpoint">GET /</span> - API documentation homepage <span class="status active">ACTIVE</span></li>
                    <li><span class="endpoint">GET /health</span> - Health check <span class="status active">ACTIVE</span></li>
                    <li><span class="endpoint">GET /api/zoning</span> - Generate dynamic zones <span class="status active">ACTIVE</span></li>
                    <li><span class="endpoint">GET /docs</span> - Swagger UI documentation <span class="status active">ACTIVE</span></li>
                </ul>

                <h2>🎯 Key Features</h2>
                <ul>
                    <li>✅ <strong>Dynamic zone creation</strong> - Real-time computation of service zones</li>
                    <li>✅ <strong>Optimal clustering</strong> - Maximum 11 customers per zone</li>
                    <li>✅ <strong>Fast computation</strong> - Zones calculated in milliseconds</li>
                    <li>✅ <strong>JSON output</strong> - Results saved to zones_output.json</li>
                </ul>

                <h2>🔗 Quick Links</h2>
                <ul>
                    <li><a href="/docs" target="_blank">📖 API Documentation (Swagger UI)</a></li>
                    <li><a href="/health" target="_blank">💚 Health Check</a></li>
                    <li><a href="/api/zoning" target="_blank">🗺️ Generate Zones</a></li>
                </ul>
            </div>
        </body>
    </html>
    """

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "Zoning API", "version": "1.0.0"}

@app.get("/api/zoning", response_model=DynamicZoningResponse)
async def get_dynamic_zoning():
    """
    Dynamic zoning endpoint - computes zones on-the-fly.
    Returns zones with maximum 11 customers each.
    """
    try:
        result = perform_dynamic_zoning()
        return DynamicZoningResponse(**result)

    except Exception as e:
        logger.error(f"Error in dynamic zoning API: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
