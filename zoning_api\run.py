#!/usr/bin/env python3
"""
Simple script to run the Zoning API server
"""
import uvicorn
from main import app

if __name__ == "__main__":
    print("🗺️ Starting Zoning API Server...")
    print("📍 API will be available at: http://localhost:8000")
    print("📖 Documentation at: http://localhost:8000/docs")
    print("🎯 Zoning endpoint: http://localhost:8000/api/zoning")
    
    uvicorn.run(
        app, 
        host="0.0.0.0", 
        port=8000,
        reload=True
    )
